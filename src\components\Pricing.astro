---
const plans = [
  {
    name: "Starter",
    price: 29,
    description: "Perfect for small teams and startups",
    features: [
      "Up to 5 team members",
      "10GB storage",
      "Basic analytics",
      "24/7 email support",
      "API access"
    ],
    cta: "Start Free Trial",
    popular: false,
    color: "primary"
  },
  {
    name: "Professional",
    price: 79,
    description: "Ideal for growing businesses",
    features: [
      "Up to 20 team members",
      "50GB storage",
      "Advanced analytics",
      "Priority support",
      "API access",
      "Custom integrations",
      "Team collaboration tools"
    ],
    cta: "Start Free Trial",
    popular: true,
    color: "secondary"
  },
  {
    name: "Enterprise",
    price: 199,
    description: "For large organizations with complex needs",
    features: [
      "Unlimited team members",
      "500GB storage",
      "Enterprise analytics",
      "Dedicated account manager",
      "API access",
      "Custom integrations",
      "Team collaboration tools",
      "Advanced security features",
      "Custom branding"
    ],
    cta: "Contact Sales",
    popular: false,
    color: "accent"
  }
];
---

<section id="pricing" class="section bg-gray-50 dark:bg-gray-800/50">
  <div class="container-custom">
    <div class="text-center max-w-3xl mx-auto mb-16">
      <span class="inline-block px-4 py-1 rounded-full bg-accent-100 dark:bg-accent-900 text-accent-600 dark:text-accent-300 font-medium text-sm mb-4">
        Pricing Plans
      </span>
      <h2 class="mb-6 text-gray-900 dark:text-white">Simple, Transparent Pricing</h2>
      <p class="text-gray-600 dark:text-gray-300">
        Choose the plan that works best for your business. All plans include a 14-day free trial.
      </p>
    </div>
    
    <div class="grid md:grid-cols-3 gap-8">
      {plans.map((plan, index) => (
        <div 
          class={`card p-6 border ${plan.popular ? `border-${plan.color}-500 dark:border-${plan.color}-400` : 'border-gray-200 dark:border-gray-700'} slide-up relative`}
          style={`animation-delay: ${index * 150}ms`}
        >
          {plan.popular && (
            <div class={`absolute top-0 right-0 bg-${plan.color}-500 text-white px-4 py-1 text-sm font-medium rounded-bl-lg rounded-tr-lg`}>
              Most Popular
            </div>
          )}
          
          <h3 class="text-xl font-semibold mb-2 text-gray-900 dark:text-white">{plan.name}</h3>
          <div class="mb-4">
            <span class="text-4xl font-bold text-gray-900 dark:text-white">${plan.price}</span>
            <span class="text-gray-500 dark:text-gray-400">/month</span>
          </div>
          <p class="text-gray-600 dark:text-gray-300 mb-6">{plan.description}</p>
          
          <ul class="space-y-3 mb-8">
            {plan.features.map(feature => (
              <li class="flex items-start">
                <svg class={`w-5 h-5 text-${plan.color}-500 mr-2 shrink-0 mt-0.5`} fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-gray-600 dark:text-gray-300">{feature}</span>
              </li>
            ))}
          </ul>
          
          <a 
            href="#signup" 
            class={`w-full btn ${plan.popular ? `bg-${plan.color}-600 hover:bg-${plan.color}-700 text-white focus:ring-${plan.color}-500` : 'btn-outline'}`}
          >
            {plan.cta}
          </a>
        </div>
      ))}
    </div>
    
    <div class="mt-12 text-center">
      <p class="text-gray-600 dark:text-gray-300 mb-2">Need a custom plan for your specific needs?</p>
      <a href="/contact" class="text-primary-600 dark:text-primary-400 font-medium hover:underline">Contact our sales team</a>
    </div>
  </div>
</section>
