{"name": "@example/saas-landing", "type": "module", "version": "0.0.1", "private": true, "scripts": {"dev": "astro dev", "build": "astro build", "preview": "astro build && astro preview", "astro": "astro", "seo:audit": "node scripts/seo-audit.js", "seo:build-and-audit": "npm run build && npm run seo:audit"}, "dependencies": {"@astrojs/alpinejs": "^0.4.5", "@astrojs/sitemap": "^3.4.0", "@tailwindcss/vite": "^4.1.3", "alpinejs": "^3.13.5", "astro": "^5.8.0", "tailwindcss": "^4.1.3"}, "devDependencies": {"jsdom": "^26.1.0"}}