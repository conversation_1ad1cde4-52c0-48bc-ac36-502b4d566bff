---
import Layout from '../layouts/Layout.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
---

<Layout title="Features - Impressive Animations & Experiences">
  <Header />
  <main>
    <!-- Hero Section -->
    <section class="relative overflow-hidden bg-linear-to-b from-primary-100 to-white dark:from-primary-950 dark:to-secondary-950 pt-24 md:pt-32 pb-16 md:pb-20">
      <div class="absolute inset-0 z-0 opacity-30">
        <div class="absolute inset-0 bg-grid-pattern"></div>
      </div>
      
      <div class="container-custom relative z-10">
        <div class="text-center max-w-3xl mx-auto px-4">
          <h1 class="text-3xl md:text-4xl lg:text-5xl mb-4 md:mb-6 text-gray-900 dark:text-white animate-slide-down">
            Cutting-Edge <span class="text-accent-500">Features</span>
          </h1>
          <p class="text-lg md:text-xl text-gray-700 dark:text-gray-300 mb-6 md:mb-8 animate-slide-up" style="animation-delay: 200ms">
            Explore our innovative features with interactive demonstrations and stunning visual effects.
          </p>
        </div>
      </div>
      
      <!-- Animated Shapes -->
      <div class="absolute -bottom-16 left-0 right-0 flex justify-center">
        <div class="w-48 md:w-64 h-48 md:h-64 bg-accent-400 rounded-full filter blur-3xl opacity-20 animate-pulse"></div>
      </div>
    </section>

    <!-- Interactive Feature Cards -->
    <section class="section relative z-10 overflow-hidden py-12 md:py-16">
      <div class="container-custom">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
          <!-- Feature Card 1: 3D Transform -->
          <div class="feature-card group perspective">
            <div class="relative h-full transform-gpu transition-all duration-700 preserve-3d hover:rotate-y-180 cursor-pointer touch-action-card">
              <div class="absolute inset-0 backface-hidden">
                <div class="card p-6 md:p-8 h-full flex flex-col items-center justify-center text-center">
                  <div class="w-14 h-14 md:w-16 md:h-16 rounded-xl bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center mb-4 md:mb-6">
                    <svg class="w-7 h-7 md:w-8 md:h-8 text-primary-600 dark:text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                    </svg>
                  </div>
                  <h3 class="text-xl font-semibold mb-3 text-gray-900 dark:text-white">3D Transformations</h3>
                  <p class="text-gray-600 dark:text-gray-300">Experience stunning 3D effects with our advanced CSS transformations.</p>
                </div>
              </div>
              <div class="absolute inset-0 backface-hidden rotate-y-180">
                <div class="card p-6 md:p-8 h-full bg-primary-600 dark:bg-primary-800 text-white flex flex-col items-center justify-center text-center">
                  <h3 class="text-xl font-semibold mb-4">How It Works</h3>
                  <p class="mb-4">Our 3D transformations use CSS perspective and transform properties to create immersive experiences.</p>
                  <ul class="text-left space-y-2 mb-4">
                    <li>• CSS perspective property</li>
                    <li>• Transform-style: preserve-3d</li>
                    <li>• Backface visibility control</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Feature Card 2: Parallax Scrolling -->
          <div class="feature-card parallax-container">
            <div class="card p-6 md:p-8 h-full">
              <div class="w-14 h-14 md:w-16 md:h-16 rounded-xl bg-accent-100 dark:bg-accent-900/30 flex items-center justify-center mb-4 md:mb-6">
                <svg class="w-7 h-7 md:w-8 md:h-8 text-accent-600 dark:text-accent-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                </svg>
              </div>
              <h3 class="text-xl font-semibold mb-3 text-gray-900 dark:text-white">Parallax Scrolling</h3>
              <p class="text-gray-600 dark:text-gray-300 mb-4 md:mb-6">Create depth and immersion with multi-layered parallax scrolling effects.</p>
              
              <div class="parallax-demo relative h-32 md:h-40 overflow-hidden rounded-lg">
                <div class="parallax-layer layer-1 absolute inset-0 bg-accent-100 dark:bg-accent-900/50"></div>
                <div class="parallax-layer layer-2 absolute inset-0 flex items-center justify-center">
                  <div class="w-16 h-16 md:w-20 md:h-20 bg-accent-300 dark:bg-accent-700 rounded-full"></div>
                </div>
                <div class="parallax-layer layer-3 absolute inset-0 flex items-end justify-center pb-4">
                  <p class="text-sm font-medium">Scroll to see the effect</p>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Feature Card 3: Particle Animation -->
          <div class="feature-card">
            <div class="card p-6 md:p-8 h-full">
              <div class="w-14 h-14 md:w-16 md:h-16 rounded-xl bg-secondary-100 dark:bg-secondary-800 flex items-center justify-center mb-4 md:mb-6">
                <svg class="w-7 h-7 md:w-8 md:h-8 text-secondary-600 dark:text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
              </div>
              <h3 class="text-xl font-semibold mb-3 text-gray-900 dark:text-white">Particle Effects</h3>
              <p class="text-gray-600 dark:text-gray-300 mb-4 md:mb-6">Dynamic particle animations that respond to user interactions.</p>
              
              <div class="particles-container relative h-32 md:h-40 bg-secondary-50 dark:bg-secondary-900 rounded-lg flex items-center justify-center overflow-hidden">
                <p class="text-sm font-medium z-10">Tap or hover to activate</p>
                <div class="particles absolute inset-0"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Interactive Demo Section -->
    <section class="section bg-gray-50 dark:bg-gray-800/50 relative overflow-hidden py-12 md:py-16">
      <div class="container-custom">
        <div class="text-center max-w-3xl mx-auto mb-8 md:mb-16 px-4">
          <span class="inline-block px-4 py-1 rounded-full bg-primary-100 dark:bg-primary-900 text-primary-600 dark:text-primary-300 font-medium text-sm mb-4">
            Interactive Demo
          </span>
          <h2 class="text-2xl md:text-3xl mb-4 md:mb-6 text-gray-900 dark:text-white">Experience Our Technology</h2>
          <p class="text-gray-600 dark:text-gray-300">
            Try our interactive demo to see how our features can transform your digital experience.
          </p>
        </div>
        
        <div class="interactive-demo bg-white dark:bg-secondary-900 rounded-xl shadow-xl overflow-hidden mx-4 sm:mx-auto">
          <div class="demo-header bg-secondary-100 dark:bg-secondary-800 p-3 md:p-4 flex items-center">
            <div class="flex space-x-2 mr-3 md:mr-4">
              <div class="w-2.5 h-2.5 md:w-3 md:h-3 rounded-full bg-red-500"></div>
              <div class="w-2.5 h-2.5 md:w-3 md:h-3 rounded-full bg-yellow-500"></div>
              <div class="w-2.5 h-2.5 md:w-3 md:h-3 rounded-full bg-green-500"></div>
            </div>
            <div class="flex-1 bg-white dark:bg-secondary-700 rounded-full h-6 md:h-8 flex items-center px-3 md:px-4 text-xs md:text-sm text-gray-500 dark:text-gray-300">
              demo.example.com
            </div>
          </div>
          <div class="demo-content p-6 md:p-8 h-64 md:h-96 flex items-center justify-center relative">
            <div class="text-center">
              <h3 class="text-xl md:text-2xl font-bold mb-3 md:mb-4 text-gray-900 dark:text-white">Interactive Canvas</h3>
              <p class="text-sm md:text-base text-gray-600 dark:text-gray-300 mb-4 md:mb-6">Tap or drag anywhere in this area to create visual effects</p>
              <button id="demo-button" class="btn-primary text-sm md:text-base px-4 py-2 md:px-6 md:py-3">
                Activate Demo
              </button>
            </div>
            <canvas id="demo-canvas" class="absolute inset-0 w-full h-full pointer-events-none opacity-50"></canvas>
          </div>
        </div>
      </div>
    </section>

    <!-- Feature Comparison -->
    <section class="section py-12 md:py-16">
      <div class="container-custom">
        <div class="text-center max-w-3xl mx-auto mb-8 md:mb-16 px-4">
          <span class="inline-block px-4 py-1 rounded-full bg-accent-100 dark:bg-accent-900 text-accent-600 dark:text-accent-400 font-medium text-sm mb-4">
            Comparison
          </span>
          <h2 class="text-2xl md:text-3xl mb-4 md:mb-6 text-gray-900 dark:text-white">How We Compare</h2>
          <p class="text-gray-600 dark:text-gray-300">
            See how our features stack up against the competition with our interactive comparison chart.
          </p>
        </div>
        
        <div class="comparison-table-container overflow-x-auto -mx-4 px-4 pb-4">
          <table class="comparison-table w-full border-collapse min-w-[640px]">
            <thead>
              <tr>
                <th class="p-3 md:p-4 text-left bg-secondary-100 dark:bg-secondary-800 text-gray-900 dark:text-white rounded-tl-lg">Feature</th>
                <th class="p-3 md:p-4 text-center bg-secondary-100 dark:bg-secondary-800 text-gray-900 dark:text-white">Basic</th>
                <th class="p-3 md:p-4 text-center bg-primary-100 dark:bg-primary-900/30 text-primary-900 dark:text-primary-100 font-bold">Premium</th>
                <th class="p-3 md:p-4 text-center bg-secondary-100 dark:bg-secondary-800 text-gray-900 dark:text-white rounded-tr-lg">Competitors</th>
              </tr>
            </thead>
            <tbody>
              <tr class="feature-row">
                <td class="p-3 md:p-4 border-b border-gray-200 dark:border-gray-700 font-medium">3D Transformations</td>
                <td class="p-3 md:p-4 border-b border-gray-200 dark:border-gray-700 text-center">Limited</td>
                <td class="p-3 md:p-4 border-b border-gray-200 dark:border-gray-700 text-center bg-primary-50 dark:bg-primary-900/10">
                  <span class="inline-flex items-center justify-center text-primary-600 dark:text-primary-400">
                    <svg class="w-4 h-4 md:w-5 md:h-5 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    Advanced
                  </span>
                </td>
                <td class="p-3 md:p-4 border-b border-gray-200 dark:border-gray-700 text-center">Basic</td>
              </tr>
              <tr class="feature-row">
                <td class="p-3 md:p-4 border-b border-gray-200 dark:border-gray-700 font-medium">Parallax Effects</td>
                <td class="p-3 md:p-4 border-b border-gray-200 dark:border-gray-700 text-center">
                  <svg class="w-5 h-5 mx-auto text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                  </svg>
                </td>
                <td class="p-3 md:p-4 border-b border-gray-200 dark:border-gray-700 text-center bg-primary-50 dark:bg-primary-900/10">
                  <span class="inline-flex items-center justify-center text-primary-600 dark:text-primary-400">
                    <svg class="w-5 h-5 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    Multi-layer
                  </span>
                </td>
                <td class="p-3 md:p-4 border-b border-gray-200 dark:border-gray-700 text-center">Limited</td>
              </tr>
              <tr class="feature-row">
                <td class="p-3 md:p-4 border-b border-gray-200 dark:border-gray-700 font-medium">Particle Animations</td>
                <td class="p-3 md:p-4 border-b border-gray-200 dark:border-gray-700 text-center">Basic</td>
                <td class="p-3 md:p-4 border-b border-gray-200 dark:border-gray-700 text-center bg-primary-50 dark:bg-primary-900/10">
                  <span class="inline-flex items-center justify-center text-primary-600 dark:text-primary-400">
                    <svg class="w-5 h-5 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    Interactive
                  </span>
                </td>
                <td class="p-3 md:p-4 border-b border-gray-200 dark:border-gray-700 text-center">Static</td>
              </tr>
              <tr class="feature-row">
                <td class="p-3 md:p-4 border-b border-gray-200 dark:border-gray-700 font-medium">Performance Optimization</td>
                <td class="p-3 md:p-4 border-b border-gray-200 dark:border-gray-700 text-center">Limited</td>
                <td class="p-3 md:p-4 border-b border-gray-200 dark:border-gray-700 text-center bg-primary-50 dark:bg-primary-900/10">
                  <span class="inline-flex items-center justify-center text-primary-600 dark:text-primary-400">
                    <svg class="w-5 h-5 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    Advanced
                  </span>
                </td>
                <td class="p-3 md:p-4 border-b border-gray-200 dark:border-gray-700 text-center">Moderate</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </section>

    <!-- Call to Action -->
    <section class="section bg-linear-to-br from-primary-600 to-primary-800 dark:from-primary-800 dark:to-primary-900 text-white py-12 md:py-16">
      <div class="container-custom">
        <div class="text-center max-w-3xl mx-auto px-4">
          <h2 class="text-2xl md:text-3xl mb-4 md:mb-6 text-white">Ready to Experience the Future?</h2>
          <p class="text-primary-100 mb-6 md:mb-8">
            Join thousands of satisfied customers who have transformed their digital presence with our cutting-edge features.
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="#signup" class="btn-accent text-sm md:text-base px-4 py-2 md:px-6 md:py-3 w-full sm:w-auto">
              Get Started Now
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 md:h-5 md:w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
            </a>
            <a href="#demo" class="btn-outline bg-white/10 text-white border-white/20 hover:bg-white/20 text-sm md:text-base px-4 py-2 md:px-6 md:py-3 w-full sm:w-auto">
              Schedule a Demo
            </a>
          </div>
        </div>
      </div>
    </section>
  </main>
  <Footer />
</Layout>

<style>
  /* Grid Pattern Background */
  .bg-grid-pattern {
    background-image: 
      linear-gradient(to right, rgba(99, 102, 241, 0.1) 1px, transparent 1px),
      linear-gradient(to bottom, rgba(99, 102, 241, 0.1) 1px, transparent 1px);
    background-size: 40px 40px;
  }

  /* 3D Card Transformations */
  .perspective {
    perspective: 1000px;
  }
  
  .preserve-3d {
    transform-style: preserve-3d;
  }
  
  .backface-hidden {
    backface-visibility: hidden;
  }
  
  .rotate-y-180 {
    transform: rotateY(180deg);
  }
  
  /* Touch device support for 3D cards */
  @media (hover: none) {
    .touch-action-card {
      transition: none;
    }
    
    .touch-action-card:active {
      transform: rotateY(180deg);
    }
  }

  /* Parallax Effect */
  .parallax-container {
    overflow: hidden;
  }
  
  .parallax-demo {
    transform-style: preserve-3d;
    perspective: 1000px;
  }
  
  .parallax-layer {
    will-change: transform;
    transition: transform 0.1s ease-out;
  }
  
  .layer-1 {
    transform: translateZ(-10px) scale(2);
  }
  
  .layer-2 {
    transform: translateZ(0);
  }
  
  .layer-3 {
    transform: translateZ(10px) scale(0.9);
  }

  /* Particle Animation */
  .particles-container:hover .particles,
  .particles-container:active .particles {
    opacity: 1;
  }
  
  .particles {
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  /* Feature Row Animation */
  .feature-row {
    transition: all 0.3s ease;
  }
  
  .feature-row:hover {
    background-color: rgba(99, 102, 241, 0.05);
  }

  /* Interactive Demo */
  .interactive-demo {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    max-width: 100%;
  }
  
  .interactive-demo:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
  
  /* Mobile optimizations */
  @media (max-width: 640px) {
    .interactive-demo:hover {
      transform: none;
    }
    
    .comparison-table {
      font-size: 0.875rem;
    }
    
    /* 修复卡片在小屏幕上的高度和定位问题 */
    .feature-card {
      position: relative;
      min-height: 280px;
      margin-bottom: 2rem;
      clear: both;
      display: block;
    }
    
    /* 确保3D卡片在翻转时内容不会溢出 */
    .preserve-3d {
      min-height: 280px;
    }
    
    /* 确保卡片内容不会重叠 */
    .card {
      height: 100%;
      position: relative;
      overflow: visible;
    }
    
    /* 修复平行滚动演示在移动设备上的显示 */
    .parallax-demo {
      height: 160px;
      margin-top: 1rem;
    }
    
    /* 确保标题和文本不会重叠 */
    h3 {
      position: relative;
      z-index: 5;
    }
  }
  
  /* 修复触摸设备上的3D卡片翻转问题 */
  @media (hover: none) {
    .touch-action-card {
      transition: transform 0.5s ease;
    }
    
    /* 确保卡片在移动设备上有足够的空间 */
    .feature-card.perspective {
      margin-bottom: 2.5rem;
    }
    
    /* 确保卡片内容在移动设备上不会重叠 */
    .feature-card.perspective .card {
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
  }
  
  /* 确保网格布局在移动设备上正确显示 */
  .grid {
    display: grid;
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  @media (min-width: 768px) {
    .grid {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  
  @media (min-width: 1024px) {
    .grid {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
</style>

<script>
  // Store references to event listeners for cleanup
  const cleanupFunctions: Array<() => void> = [];

  // Function to initialize all interactive features
  function initializeFeatures() {
    // Initialize Parallax Effect
    initializeParallax();
    
    // Initialize Particle Animation
    initializeParticles();
    
    // Initialize Interactive Demo Canvas
    initializeCanvas();
    
    // Initialize touch support for 3D cards
    initializeTouchCards();
  }
  
  // Add touch support for 3D cards
  function initializeTouchCards() {
    const cards = document.querySelectorAll('.feature-card.perspective');
    
    cards.forEach(card => {
      const cardInner = card.querySelector('.preserve-3d');
      
      if (cardInner) {
        // 使用点击事件而不是touchstart，这样更可靠
        const handleClick = () => {
          if (window.matchMedia('(hover: none)').matches) {
            cardInner.classList.toggle('rotate-y-180');
          }
        };
        
        cardInner.addEventListener('click', handleClick);
        
        // Add cleanup function
        cleanupFunctions.push(() => {
          if (cardInner) {
            cardInner.removeEventListener('click', handleClick);
          }
        });
      }
    });
  }

  // Parallax Effect
  function initializeParallax() {
    const parallaxContainer = document.querySelector('.parallax-demo');
    if (parallaxContainer) {
      const layers = parallaxContainer.querySelectorAll('.parallax-layer');
      
      const handleMouseMove = (e: MouseEvent) => {
        const containerRect = parallaxContainer.getBoundingClientRect();
        const relX = e.clientX - containerRect.left;
        const relY = e.clientY - containerRect.top;
        
        const xPercent = relX / containerRect.width - 0.5;
        const yPercent = relY / containerRect.height - 0.5;
        
        layers.forEach((layer, index) => {
          const depth = index * 10;
          const moveX = xPercent * depth;
          const moveY = yPercent * depth;
          
          (layer as HTMLElement).style.transform = `translate3d(${moveX}px, ${moveY}px, 0)`;
        });
      };
      
      const handleTouchMove = (e: TouchEvent) => {
        if (e.touches.length > 0) {
          const containerRect = parallaxContainer.getBoundingClientRect();
          const touch = e.touches[0];
          const relX = touch.clientX - containerRect.left;
          const relY = touch.clientY - containerRect.top;
          
          const xPercent = relX / containerRect.width - 0.5;
          const yPercent = relY / containerRect.height - 0.5;
          
          layers.forEach((layer, index) => {
            const depth = index * 10;
            const moveX = xPercent * depth;
            const moveY = yPercent * depth;
            
            (layer as HTMLElement).style.transform = `translate3d(${moveX}px, ${moveY}px, 0)`;
          });
        }
      };
      
      const handleMouseLeave = () => {
        layers.forEach((layer) => {
          (layer as HTMLElement).style.transform = 'translate3d(0, 0, 0)';
        });
      };
      
      const handleTouchEnd = () => {
        layers.forEach((layer) => {
          (layer as HTMLElement).style.transform = 'translate3d(0, 0, 0)';
        });
      };
      
      // Use type assertion to fix TypeScript errors
      (parallaxContainer as HTMLElement).addEventListener('mousemove', handleMouseMove);
      (parallaxContainer as HTMLElement).addEventListener('touchmove', handleTouchMove);
      (parallaxContainer as HTMLElement).addEventListener('mouseleave', handleMouseLeave);
      (parallaxContainer as HTMLElement).addEventListener('touchend', handleTouchEnd);
      
      // Add cleanup function
      cleanupFunctions.push(() => {
        if (parallaxContainer) {
          (parallaxContainer as HTMLElement).removeEventListener('mousemove', handleMouseMove);
          (parallaxContainer as HTMLElement).removeEventListener('touchmove', handleTouchMove);
          (parallaxContainer as HTMLElement).removeEventListener('mouseleave', handleMouseLeave);
          (parallaxContainer as HTMLElement).removeEventListener('touchend', handleTouchEnd);
        }
      });
    }
  }

  // Particle Animation
  function initializeParticles() {
    const particlesContainer = document.querySelector('.particles-container');
    const particlesElement = document.querySelector('.particles');
    
    if (particlesContainer && particlesElement) {
      const createParticles = () => {
        particlesElement.innerHTML = '';
        const containerRect = particlesContainer.getBoundingClientRect();
        
        // Reduce particle count on mobile
        const particleCount = window.innerWidth < 768 ? 15 : 30;
        
        for (let i = 0; i < particleCount; i++) {
          const particle = document.createElement('div');
          particle.classList.add('particle');
          
          const size = Math.random() * 6 + 2;
          const x = Math.random() * containerRect.width;
          const y = Math.random() * containerRect.height;
          const duration = Math.random() * 2 + 1;
          const delay = Math.random() * 0.5;
          
          particle.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            background-color: var(--tw-color-primary-400, #a78bfa);
            border-radius: 50%;
            left: ${x}px;
            top: ${y}px;
            opacity: 0;
            animation: float ${duration}s ease-in-out ${delay}s infinite alternate;
          `;
          
          particlesElement.appendChild(particle);
        }
      };
      
      createParticles();
      
      // Add touch support
      const handleTouch = () => {
        (particlesContainer as HTMLElement).classList.add('active');
        setTimeout(() => {
          (particlesContainer as HTMLElement).classList.remove('active');
        }, 1000);
      };
      
      (particlesContainer as HTMLElement).addEventListener('touchstart', handleTouch);
      
      // Add animation keyframes
      const style = document.createElement('style');
      style.textContent = `
        @keyframes float {
          0% {
            transform: translateY(0) translateX(0);
            opacity: 0;
          }
          50% {
            opacity: 0.8;
          }
          100% {
            transform: translateY(-20px) translateX(10px);
            opacity: 0;
          }
        }
        
        .particles-container.active .particles {
          opacity: 1;
        }
      `;
      document.head.appendChild(style);
      
      // Add cleanup function
      cleanupFunctions.push(() => {
        if (style && document.head.contains(style)) {
          document.head.removeChild(style);
        }
        
        if (particlesContainer) {
          (particlesContainer as HTMLElement).removeEventListener('touchstart', handleTouch);
        }
      });
    }
  }

  // Interactive Demo Canvas
  function initializeCanvas() {
    const canvas = document.getElementById('demo-canvas') as HTMLCanvasElement;
    const demoButton = document.getElementById('demo-button') as HTMLButtonElement;
    
    if (canvas && demoButton) {
      const ctx = canvas.getContext('2d');
      let isActive = false;
      let particles: Array<Particle> = [];
      let animationFrameId: number | null = null;
      
      // Resize canvas to match container
      const resizeCanvas = () => {
        const container = canvas.parentElement;
        if (container) {
          canvas.width = container.offsetWidth;
          canvas.height = container.offsetHeight;
        }
      };
      
      resizeCanvas();
      window.addEventListener('resize', resizeCanvas);
      
      // Particle class
      class Particle {
        x: number;
        y: number;
        size: number;
        color: string;
        speedX: number;
        speedY: number;
        life: number;
        
        constructor(x: number, y: number, color?: string) {
          this.x = x;
          this.y = y;
          this.size = Math.random() * 5 + 2;
          this.color = color || `hsl(${Math.random() * 60 + 240}, 70%, 60%)`;
          this.speedX = Math.random() * 3 - 1.5;
          this.speedY = Math.random() * 3 - 1.5;
          this.life = 100;
        }
        
        update() {
          this.x += this.speedX;
          this.y += this.speedY;
          this.life -= 1;
          
          if (this.size > 0.2) this.size -= 0.1;
        }
        
        draw() {
          if (ctx) {
            ctx.fillStyle = this.color;
            ctx.beginPath();
            ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
            ctx.fill();
          }
        }
      }
      
      // Animation loop
      function animate() {
        if (!isActive) return;
        
        if (ctx) {
          ctx.clearRect(0, 0, canvas.width, canvas.height);
          
          for (let i = 0; i < particles.length; i++) {
            particles[i].update();
            particles[i].draw();
            
            if (particles[i].life <= 0 || particles[i].size <= 0.2) {
              particles.splice(i, 1);
              i--;
            }
          }
        }
        
        animationFrameId = requestAnimationFrame(animate);
      }
      
      // Event listeners
      const handleButtonClick = () => {
        isActive = !isActive;
        canvas.style.opacity = isActive ? '1' : '0.5';
        canvas.style.pointerEvents = isActive ? 'auto' : 'none';
        demoButton.textContent = isActive ? 'Deactivate Demo' : 'Activate Demo';
        
        if (isActive) {
          animate();
        } else if (animationFrameId) {
          cancelAnimationFrame(animationFrameId);
          animationFrameId = null;
        }
      };
      
      const handleMouseMove = (e: MouseEvent) => {
        if (!isActive) return;
        
        const rect = canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        for (let i = 0; i < 3; i++) {
          particles.push(new Particle(x, y));
        }
      };
      
      const handleClick = (e: MouseEvent) => {
        if (!isActive) return;
        
        const rect = canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        for (let i = 0; i < 20; i++) {
          particles.push(new Particle(x, y, `hsl(${Math.random() * 60 + 40}, 100%, 60%)`));
        }
      };
      
      demoButton.addEventListener('click', handleButtonClick);
      canvas.addEventListener('mousemove', handleMouseMove);
      canvas.addEventListener('click', handleClick);
      
      // Add cleanup function
      cleanupFunctions.push(() => {
        if (animationFrameId) {
          cancelAnimationFrame(animationFrameId);
        }
        
        window.removeEventListener('resize', resizeCanvas);
        
        if (demoButton) {
          demoButton.removeEventListener('click', handleButtonClick);
        }
        
        if (canvas) {
          canvas.removeEventListener('mousemove', handleMouseMove);
          canvas.removeEventListener('click', handleClick);
        }
        
        // Clear particles array
        particles = [];
      });
    }
  }
  
  // Initialize features on initial page load
  document.addEventListener('DOMContentLoaded', initializeFeatures);
  
  // Initialize features when navigating to this page with Astro's view transitions
  document.addEventListener('astro:page-load', initializeFeatures);
  
  // Cleanup when page is unloaded or before navigation
  document.addEventListener('astro:before-swap', () => {
    // Execute all cleanup functions
    cleanupFunctions.forEach(cleanup => cleanup());
    // Clear the array
    cleanupFunctions.length = 0;
  });
  
  // Also clean up on window unload as a fallback
  window.addEventListener('unload', () => {
    cleanupFunctions.forEach(cleanup => cleanup());
  });
</script>
