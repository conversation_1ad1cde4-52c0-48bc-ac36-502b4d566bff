// src/middleware.ts
import { defineMiddleware } from 'astro:middleware';
// Ensure this path is correct relative to the project root.
// Astro typically handles `src` imports well.
import locationsData from './data/locations_pl.json';
import { detectBrowserLanguage } from './i18n/utils';

console.log('[Middleware] Initializing middleware...');

const knownSlugs = new Set<string>();
if (Array.isArray(locationsData)) {
  locationsData.forEach(region => {
    // Ensure region and region.slug are valid
    if (region && typeof region.slug === 'string' && region.slug.length > 0) {
      knownSlugs.add(region.slug);
      // Ensure region.children is an array before iterating
      if (region.children && Array.isArray(region.children)) {
        region.children.forEach((branch: any) => {
          // Ensure branch and branch.slug are valid, and region.slug is available
          if (branch && typeof branch.slug === 'string' && branch.slug.length > 0) {
            knownSlugs.add(`${region.slug}/${branch.slug}`);
          }
        });
      }
    }
  });
} else {
  console.error('[Middleware] FATAL: locationsData is not an array or is undefined. Check the import path and file content of locations_pl.json.');
}

// Log the size and some content of knownSlugs for debugging
console.log(`[Middleware] Known slugs loaded. Count: ${knownSlugs.size}`);
if (knownSlugs.size > 0 && knownSlugs.size < 20) { // Log a few examples if the set is small
  console.log('[Middleware] Example known slugs (up to 5):', Array.from(knownSlugs).slice(0, 5));
} else if (knownSlugs.size === 0) {
  console.warn('[Middleware] WARNING: knownSlugs is empty. No redirects will occur based on slugs.');
}


export const onRequest = defineMiddleware(async (context, next) => {
  const { url, redirect } = context;
  const pathname = url.pathname;
  
  console.log(`[Middleware] Request for: "${pathname}"`);

  // Normalize the pathname: remove leading slash, then remove trailing slash for consistent key format
  let normalizedPathKey = pathname;
  if (normalizedPathKey.startsWith('/')) {
    normalizedPathKey = normalizedPathKey.slice(1);
  }
  if (normalizedPathKey.endsWith('/')) {
    normalizedPathKey = normalizedPathKey.slice(0, -1);
  }
  
  console.log(`[Middleware] Normalized path key for matching: "${normalizedPathKey}"`);

  // --- Section 1: Handle /pl/... paths missing /polska/ ---
  if (normalizedPathKey.startsWith('pl/') && !normalizedPathKey.startsWith('pl/polska/')) {
    const potentialSlug = normalizedPathKey.substring('pl/'.length);
    console.log(`[Middleware] Section 1: Checking PL slug (missing /polska/): "${potentialSlug}"`);
    
    if (knownSlugs.has(potentialSlug)) {
      const targetPath = `/pl/polska/${potentialSlug}/`;
      console.log(`[Middleware] Section 1: Redirecting "${pathname}" to "${targetPath}"`);
      return redirect(targetPath, 302);
    } else {
      console.log(`[Middleware] Section 1: Slug "${potentialSlug}" not in knownSlugs.`);
    }
  }

  // --- Section 2: Handle root-level regional slugs ---
  const rootLevelPrefixesToIgnore = [
    'pl/', 'en/', 'poland/',
    'assets/', '_astro/',
    'favicon.', 'robots.txt', 'sitemap.xml', 'sitemap-index.xml',
    'api/'
  ];

  const isSection2IgnoredPath = rootLevelPrefixesToIgnore.some(prefix => normalizedPathKey.startsWith(prefix)) || normalizedPathKey === '';

  if (!isSection2IgnoredPath) {
    console.log(`[Middleware] Section 2: Checking root-level slug: "${normalizedPathKey}"`);
    if (knownSlugs.has(normalizedPathKey)) {
      const targetLang = detectBrowserLanguage();
      console.log(`[Middleware] Section 2: Detected language "${targetLang}" for slug "${normalizedPathKey}"`);

      let targetPathBase = '';
      if (targetLang === 'pl') {
        targetPathBase = `/pl/polska/`;
      } else { 
        targetPathBase = `/poland/`;
      }
      const targetPath = `${targetPathBase}${normalizedPathKey}/`;

      if (decodeURIComponent(pathname) === decodeURIComponent(targetPath)) {
        console.log(`[Middleware] Section 2: Path "${pathname}" is already the target. No redirect needed.`);
        return next();
      }
      
      console.log(`[Middleware] Section 2: Redirecting "${pathname}" to "${targetPath}"`);
      return redirect(targetPath, 302);
    } else {
      console.log(`[Middleware] Section 2: Root-level slug "${normalizedPathKey}" not in knownSlugs.`);
    }
  } else {
    // This log can be noisy, uncomment if specifically debugging why Section 2 is skipped
    // console.log(`[Middleware] Path "${normalizedPathKey}" (from "${pathname}") is ignored by Section 2 logic.`);
  }
  
  // console.log(`[Middleware] No redirect conditions met for "${pathname}". Passing to next().`);
  return next();
});
