/**
 * Test script for road search functionality
 * Run this in the browser console on the homepage or rest areas page
 */

// Test data extraction
function testDataExtraction() {
  console.log('Testing data extraction...');

  // Check if highway data is available
  if (window.uniqueHighways) {
    console.log('✅ Highway data loaded:', window.uniqueHighways);
  } else {
    console.log('❌ Highway data not found');
  }

  // Check if location data is available
  if (window.locationsData) {
    console.log('✅ Location data loaded:', window.locationsData.length, 'regions');
  } else {
    console.log('❌ Location data not found');
  }
}

// Test search functionality on homepage
function testHomepageSearch() {
  console.log('Testing homepage search...');

  // Check if Alpine.js component is available
  const searchComponent = document.querySelector('[x-data*="locationSearch"]');
  if (searchComponent) {
    console.log('✅ Search component found');

    // Test highway data in component
    const alpineData = Alpine.$data(searchComponent);
    if (alpineData && alpineData.highways) {
      console.log('✅ Highway data in component:', alpineData.highways.length, 'highways');
      console.log('✅ Active tab:', alpineData.activeTab);

      // Test tab switching
      console.log('Testing tab switching...');
      alpineData.switchTab('highway');
      console.log('✅ Switched to highway tab:', alpineData.activeTab);
      alpineData.switchTab('text');
      console.log('✅ Switched to text tab:', alpineData.activeTab);
      alpineData.switchTab('location');
      console.log('✅ Switched back to location tab:', alpineData.activeTab);
    } else {
      console.log('❌ Highway data not accessible in component');
    }
  } else {
    console.log('❌ Search component not found');
  }
}

// Test rest areas page filtering
function testRestAreasFiltering() {
  console.log('Testing rest areas filtering...');

  // Check if rest area elements exist
  const restAreas = document.querySelectorAll('[data-rest-area]');
  console.log('✅ Found', restAreas.length, 'rest areas');

  // Check data attributes
  if (restAreas.length > 0) {
    const firstArea = restAreas[0];
    const attributes = ['data-title', 'data-highway', 'data-location', 'data-address'];
    attributes.forEach(attr => {
      if (firstArea.hasAttribute(attr)) {
        console.log('✅', attr, ':', firstArea.getAttribute(attr));
      } else {
        console.log('❌', attr, 'missing');
      }
    });
  }

  // Test filter component
  const filterComponent = document.querySelector('[x-data*="searchTerm"]');
  if (filterComponent) {
    console.log('✅ Filter component found');

    const alpineData = Alpine.$data(filterComponent);
    if (alpineData) {
      console.log('✅ Filter data accessible');
      console.log('  - Search term:', alpineData.searchTerm);
      console.log('  - Selected highway:', alpineData.selectedHighway);
      console.log('  - Available highways:', alpineData.highways?.length || 0);
    }
  } else {
    console.log('❌ Filter component not found');
  }
}

// Test URL parameter handling
function testURLParameters() {
  console.log('Testing URL parameters...');

  const params = new URLSearchParams(window.location.search);
  const search = params.get('search');
  const highway = params.get('highway');

  if (search) {
    console.log('✅ Search parameter found:', search);
  }

  if (highway) {
    console.log('✅ Highway parameter found:', highway);
  }

  if (!search && !highway) {
    console.log('ℹ️ No URL parameters (this is normal for clean URLs)');
  }
}

// Test search routing
function testSearchRouting() {
  console.log('Testing search routing...');

  // Test highway search URL
  const highwayURL = '/rest-areas/?highway=A1';
  console.log('✅ Highway search URL format:', highwayURL);

  // Test text search URL
  const textURL = '/rest-areas/?search=Jeżewo';
  console.log('✅ Text search URL format:', textURL);

  // Test location search URL
  const locationURL = '/pl/podlaskie/bialystok/';
  console.log('✅ Location search URL format:', locationURL);
}

// Run all tests
function runAllTests() {
  console.log('🚀 Starting road search functionality tests...\n');

  testDataExtraction();
  console.log('');

  testURLParameters();
  console.log('');

  if (window.location.pathname === '/') {
    testHomepageSearch();
  } else if (window.location.pathname === '/rest-areas/') {
    testRestAreasFiltering();
  }
  console.log('');

  testSearchRouting();
  console.log('');

  console.log('✅ All tests completed!');
  console.log('💡 To test manually:');
  console.log('   1. Try different search tabs on homepage');
  console.log('   2. Search for highways like "A1", "S8"');
  console.log('   3. Search for text like "Jeżewo", "Malankowo"');
  console.log('   4. Use location dropdowns');
  console.log('   5. Check URL parameters are preserved');
}

// Auto-run tests when script is loaded
if (typeof window !== 'undefined') {
  // Wait for Alpine.js to initialize
  document.addEventListener('alpine:init', () => {
    setTimeout(runAllTests, 1000);
  });

  // Fallback if Alpine.js is already initialized
  if (window.Alpine) {
    setTimeout(runAllTests, 1000);
  }
}

// Export for manual testing
if (typeof module !== 'undefined') {
  module.exports = {
    testDataExtraction,
    testHomepageSearch,
    testRestAreasFiltering,
    testURLParameters,
    testSearchRouting,
    runAllTests
  };
}
