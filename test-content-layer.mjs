#!/usr/bin/env node

/**
 * Test script to verify Content Layer API migration
 * This script tests that the content collections are working correctly
 * after migrating from legacy Content Collections API to Content Layer API
 */

import { getCollection } from 'astro:content';

async function testContentLayer() {
  console.log('🧪 Testing Content Layer API Migration...\n');

  try {
    // Test 1: Get all rest areas
    console.log('📋 Test 1: Fetching all rest areas...');
    const allRestAreas = await getCollection('rest-areas');
    console.log(`✅ Found ${allRestAreas.length} rest areas`);
    
    if (allRestAreas.length === 0) {
      console.log('❌ No rest areas found - this might indicate an issue');
      return false;
    }

    // Test 2: Check data structure
    console.log('\n🔍 Test 2: Checking data structure...');
    const firstRestArea = allRestAreas[0];
    console.log(`✅ First rest area ID: ${firstRestArea.id}`);
    console.log(`✅ Title: ${firstRestArea.data.title}`);
    console.log(`✅ Location: ${firstRestArea.data.address_line}`);
    console.log(`✅ Highway: ${firstRestArea.data.highway_tag}`);

    // Test 3: Check required properties
    console.log('\n🔧 Test 3: Validating required properties...');
    const requiredProps = ['title', 'description_short', 'address_line', 'coordinates', 'amenities'];
    let allPropsValid = true;
    
    for (const prop of requiredProps) {
      if (!firstRestArea.data[prop]) {
        console.log(`❌ Missing property: ${prop}`);
        allPropsValid = false;
      } else {
        console.log(`✅ Property ${prop}: OK`);
      }
    }

    // Test 4: Check amenities structure
    console.log('\n🏪 Test 4: Checking amenities structure...');
    const amenities = firstRestArea.data.amenities;
    const amenityKeys = Object.keys(amenities);
    console.log(`✅ Found ${amenityKeys.length} amenity types`);
    console.log(`✅ Sample amenities: ${amenityKeys.slice(0, 5).join(', ')}`);

    // Test 5: Check coordinates
    console.log('\n🗺️ Test 5: Validating coordinates...');
    const coords = firstRestArea.data.coordinates;
    if (coords.lat && coords.lon) {
      console.log(`✅ Coordinates: ${coords.lat}, ${coords.lon}`);
    } else {
      console.log('❌ Invalid coordinates');
      allPropsValid = false;
    }

    console.log('\n🎉 Content Layer API Migration Test Results:');
    console.log(`📊 Total rest areas: ${allRestAreas.length}`);
    console.log(`🔧 Data structure: ${allPropsValid ? 'Valid' : 'Invalid'}`);
    console.log(`✨ Migration status: ${allPropsValid ? 'SUCCESS' : 'NEEDS ATTENTION'}`);

    return allPropsValid;

  } catch (error) {
    console.error('❌ Error testing Content Layer API:', error.message);
    return false;
  }
}

// Run the test
testContentLayer().then(success => {
  process.exit(success ? 0 : 1);
});
