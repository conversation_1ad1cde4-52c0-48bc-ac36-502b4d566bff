---
import Layout from '../layouts/Layout.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
import HeroSection from '../components/HeroSection.astro';
---

<Layout title="Case Studies - Sassify">
  <Header />
  <main>
    <HeroSection 
      title="Case Studies" 
      highlightText="Studies"
      description="See how our customers are achieving success with Sassify."
    />
    
    <section class="py-16 bg-white dark:bg-gray-800">
      <div class="container-custom">
        <!-- Case Study 1 -->
        <div class="mb-24">
          <div class="grid md:grid-cols-2 gap-12 items-center mb-12">
            <div>
              <span class="inline-block px-4 py-1 rounded-full bg-secondary-100 dark:bg-secondary-900 text-secondary-600 dark:text-secondary-300 font-medium text-sm mb-4">
                E-commerce
              </span>
              <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">How ShopWave Increased Conversions by 45%</h2>
              <p class="text-gray-600 dark:text-gray-300 mb-6">
                ShopWave, a leading e-commerce platform, was struggling with cart abandonment and low conversion rates. Learn how they used Sassify to transform their customer experience and achieve remarkable results.
              </p>
              <div class="flex flex-wrap gap-4 mb-6">
                <div class="flex items-center">
                  <svg class="w-5 h-5 text-primary-600 dark:text-primary-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  <span class="text-gray-700 dark:text-gray-300">45% Conversion Increase</span>
                </div>
                <div class="flex items-center">
                  <svg class="w-5 h-5 text-primary-600 dark:text-primary-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  <span class="text-gray-700 dark:text-gray-300">30% Cart Abandonment Reduction</span>
                </div>
                <div class="flex items-center">
                  <svg class="w-5 h-5 text-primary-600 dark:text-primary-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  <span class="text-gray-700 dark:text-gray-300">2.5x ROI</span>
                </div>
              </div>
              <a href="#" class="btn-primary">Read Full Case Study</a>
            </div>
            <div class="rounded-lg overflow-hidden shadow-xl">
              <img src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80" alt="ShopWave Dashboard" class="w-full h-auto" />
            </div>
          </div>
        </div>
        
        <!-- Case Study 2 -->
        <div class="mb-24">
          <div class="grid md:grid-cols-2 gap-12 items-center mb-12">
            <div class="order-2 md:order-1 rounded-lg overflow-hidden shadow-xl">
              <img src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80" alt="TechNova Team" class="w-full h-auto" />
            </div>
            <div class="order-1 md:order-2">
              <span class="inline-block px-4 py-1 rounded-full bg-secondary-100 dark:bg-secondary-900 text-secondary-600 dark:text-secondary-300 font-medium text-sm mb-4">
                SaaS
              </span>
              <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">TechNova's Journey to 10,000 Active Users</h2>
              <p class="text-gray-600 dark:text-gray-300 mb-6">
                TechNova, a startup offering AI-powered analytics, needed to scale their infrastructure while maintaining performance. Discover how Sassify helped them grow from 1,000 to 10,000 active users without increasing their engineering team.
              </p>
              <div class="flex flex-wrap gap-4 mb-6">
                <div class="flex items-center">
                  <svg class="w-5 h-5 text-primary-600 dark:text-primary-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  <span class="text-gray-700 dark:text-gray-300">10x User Growth</span>
                </div>
                <div class="flex items-center">
                  <svg class="w-5 h-5 text-primary-600 dark:text-primary-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  <span class="text-gray-700 dark:text-gray-300">40% Cost Reduction</span>
                </div>
                <div class="flex items-center">
                  <svg class="w-5 h-5 text-primary-600 dark:text-primary-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  <span class="text-gray-700 dark:text-gray-300">99.9% Uptime</span>
                </div>
              </div>
              <a href="#" class="btn-primary">Read Full Case Study</a>
            </div>
          </div>
        </div>
        
        <!-- Case Study 3 -->
        <div>
          <div class="grid md:grid-cols-2 gap-12 items-center mb-12">
            <div>
              <span class="inline-block px-4 py-1 rounded-full bg-secondary-100 dark:bg-secondary-900 text-secondary-600 dark:text-secondary-300 font-medium text-sm mb-4">
                Healthcare
              </span>
              <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">MediConnect's Digital Transformation</h2>
              <p class="text-gray-600 dark:text-gray-300 mb-6">
                MediConnect, a healthcare provider network, needed to modernize their patient management system. See how Sassify helped them transition from legacy systems to a modern, secure, and compliant digital platform.
              </p>
              <div class="flex flex-wrap gap-4 mb-6">
                <div class="flex items-center">
                  <svg class="w-5 h-5 text-primary-600 dark:text-primary-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  <span class="text-gray-700 dark:text-gray-300">60% Efficiency Improvement</span>
                </div>
                <div class="flex items-center">
                  <svg class="w-5 h-5 text-primary-600 dark:text-primary-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  <span class="text-gray-700 dark:text-gray-300">HIPAA Compliant</span>
                </div>
                <div class="flex items-center">
                  <svg class="w-5 h-5 text-primary-600 dark:text-primary-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  <span class="text-gray-700 dark:text-gray-300">3 Month Implementation</span>
                </div>
              </div>
              <a href="#" class="btn-primary">Read Full Case Study</a>
            </div>
            <div class="rounded-lg overflow-hidden shadow-xl">
              <img src="https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80" alt="MediConnect Platform" class="w-full h-auto" />
            </div>
          </div>
        </div>
        
        <div class="mt-16 text-center">
          <h3 class="text-2xl font-semibold text-gray-900 dark:text-white mb-4">Ready to achieve similar results?</h3>
          <p class="text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
            Join hundreds of companies that are transforming their businesses with Sassify. Our team is ready to help you succeed.
          </p>
          <div class="flex flex-col sm:flex-row justify-center gap-4">
            <a href="/contact" class="btn-primary">Contact Sales</a>
            <a href="/pricing" class="btn-secondary">View Pricing</a>
          </div>
        </div>
      </div>
    </section>
  </main>
  <Footer />
</Layout> 
