---
export interface Props {
  amenityName: string;
  isPresent: boolean;
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  variant?: 'primary' | 'secondary';
  tooltip?: string;
}

const { amenityName, isPresent, size = 'md', showLabel = true, variant = 'primary', tooltip } = Astro.props;

const amenityConfig = {
  toilets: { icon: '🚻', label: 'Toilets' },
  wifi: { icon: '📶', label: 'WiFi' },
  fuel_station: { icon: '⛽', label: 'Fuel Station' },
  restaurant: { icon: '🍽️', label: 'Restaurant' },
  shop: { icon: '🛒', label: 'Shop' },
  playground: { icon: '🎠', label: 'Playground' },
  showers: { icon: '🚿', label: 'Showers' },
  car_wash: { icon: '🚗', label: 'Car Wash' },
  ev_charging: { icon: '🔌', label: 'EV Charging' },
  security: { icon: '👮', label: 'Security' },
  cctv: { icon: '📹', label: 'CCTV' },
  lighting: { icon: '💡', label: 'Lighting' },
  accommodation: { icon: '🏨', label: 'Accommodation' },
  fenced_area: { icon: '🚧', label: 'Fenced Area' },
  vehicle_workshop: { icon: '🔧', label: 'Vehicle Workshop' },
  liquid_waste_disposal_rv: { icon: '🚛', label: 'RV Waste Disposal' },
  hydrogen_fueling: { icon: '⚡', label: 'Hydrogen Fuel' },
  cng_fueling: { icon: '🔥', label: 'CNG Fuel' },
  lng_fueling: { icon: '❄️', label: 'LNG Fuel' },
  snow_removal_ramp_trucks: { icon: '🚜', label: 'Snow Removal Ramp' },
  toilets_accessible: { icon: '♿', label: 'Accessible Toilets' },
  security_personnel_on_site: { icon: '👨‍✈️', label: 'Security Personnel' }
};

const config = amenityConfig[amenityName as keyof typeof amenityConfig];
if (!config) return null;

const sizeClasses = {
  sm: 'text-sm p-3',
  md: 'text-base p-4',
  lg: 'text-lg p-5'
};

const iconSizes = {
  sm: 'text-lg',
  md: 'text-xl',
  lg: 'text-2xl'
};
---

{isPresent && (
  <div
    class={`inline-flex items-center ${sizeClasses[size]} rounded-lg transition-all duration-200 hover:scale-105 hover:shadow-md ${tooltip ? 'cursor-help' : 'cursor-default'} ${
      variant === 'primary'
        ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 hover:bg-green-200 dark:hover:bg-green-800'
        : 'bg-secondary-50 dark:bg-secondary-800 text-secondary-600 dark:text-secondary-400 hover:bg-secondary-100 dark:hover:bg-secondary-700 border border-secondary-200 dark:border-secondary-700'
    } relative group min-w-0 max-w-full`}
    title={tooltip}
  >
    <span class={iconSizes[size]} role="img" aria-label={config.label}>
      {config.icon}
    </span>
    {showLabel && (
      <span class="ml-3 text-sm font-medium flex items-center min-w-0 flex-1">
        <span class="truncate">{config.label}</span>
        {tooltip && (
          <span class="ml-1 text-xs opacity-60 flex-shrink-0">ℹ️</span>
        )}
      </span>
    )}
    {tooltip && (
      <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10 shadow-lg">
        {tooltip}
        <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900 dark:border-t-gray-100"></div>
      </div>
    )}
  </div>
)}
