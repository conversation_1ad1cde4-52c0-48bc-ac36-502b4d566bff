---
export interface Props {
  parkingSpacesCars?: number;
  parkingSpacesTrucks?: number;
  parkingSpacesBuses?: number;
  parkingSpacesDangerous?: number;
}

const {
  parkingSpacesCars,
  parkingSpacesTrucks,
  parkingSpacesBuses,
  parkingSpacesDangerous
} = Astro.props;

// Only show badges if we have parking data
const hasAnyParkingData = parkingSpacesCars || parkingSpacesTrucks || parkingSpacesBuses || parkingSpacesDangerous;
---

{hasAnyParkingData && (
  <div class="flex flex-wrap gap-3">
    {parkingSpacesCars && parkingSpacesCars > 0 && (
      <div class="inline-flex items-center px-4 py-2 rounded-lg text-lg font-semibold bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 border border-blue-200 dark:border-blue-800">
        <svg class="w-6 h-6 mr-3" fill="currentColor" viewBox="0 0 24 24">
          <path d="M5 11l1.5-4.5h11L19 11v5c0 .55-.45 1-1 1s-1-.45-1-1v-1H7v1c0 .55-.45 1-1 1s-1-.45-1-1v-5zM6.5 13.5c.83 0 1.5-.67 1.5-1.5s-.67-1.5-1.5-1.5S5 11.17 5 12s.67 1.5 1.5 1.5zm11 0c.83 0 1.5-.67 1.5-1.5s-.67-1.5-1.5-1.5S16 11.17 16 12s.67 1.5 1.5 1.5z"/>
        </svg>
        <span class="text-2xl font-bold mr-2">{parkingSpacesCars}</span>
        <span class="text-sm">Cars</span>
      </div>
    )}

    {parkingSpacesTrucks && parkingSpacesTrucks > 0 && (
      <div class="inline-flex items-center px-4 py-2 rounded-lg text-lg font-semibold bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 border border-blue-200 dark:border-blue-800">
        <svg class="w-6 h-6 mr-3" fill="currentColor" viewBox="0 0 24 24">
          <path d="M20 8h-3V4H3c-1.1 0-2 .9-2 2v11h2c0 1.66 1.34 3 3 3s3-1.34 3-3h6c0 1.66 1.34 3 3 3s3-1.34 3-3h2v-5l-3-4zM6 18.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zm13.5-9l1.96 2.5H17V9.5h2.5zm-1.5 9c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5z"/>
        </svg>
        <span class="text-2xl font-bold mr-2">{parkingSpacesTrucks}</span>
        <span class="text-sm">TIRs</span>
      </div>
    )}

    {parkingSpacesBuses && parkingSpacesBuses > 0 && (
      <div class="inline-flex items-center px-4 py-2 rounded-lg text-lg font-semibold bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 border border-blue-200 dark:border-blue-800">
        <svg class="w-6 h-6 mr-3" fill="currentColor" viewBox="0 0 24 24">
          <path d="M4 16c0 .88.39 1.67 1 2.22V20c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h8v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1.78c.61-.55 1-1.34 1-2.22V6c0-3.5-3.58-4-8-4s-8 .5-8 4v10zm3.5 1c-.83 0-1.5-.67-1.5-1.5S6.67 14 7.5 14s1.5.67 1.5 1.5S8.33 17 7.5 17zm9 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zm1.5-6H6V6h12v5z"/>
        </svg>
        <span class="text-2xl font-bold mr-2">{parkingSpacesBuses}</span>
        <span class="text-sm">Buses</span>
      </div>
    )}

    {parkingSpacesDangerous && parkingSpacesDangerous > 0 && (
      <div class="inline-flex items-center px-4 py-2 rounded-lg text-lg font-semibold bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200 border border-orange-200 dark:border-orange-800 relative group">
        <svg class="w-6 h-6 mr-3" fill="currentColor" viewBox="0 0 24 24">
          <path d="M1 9l2 12h18l2-12H1zm8.5 5c0 .83-.67 1.5-1.5 1.5s-1.5-.67-1.5-1.5.67-1.5 1.5-1.5 1.5.67 1.5 1.5zm7 0c0 .83-.67 1.5-1.5 1.5s-1.5-.67-1.5-1.5.67-1.5 1.5-1.5 1.5.67 1.5 1.5zM12 1L3 5v2h18V5l-9-4z"/>
          <path d="M12 8l-1.5 1.5L12 11l1.5-1.5L12 8z" fill="white"/>
        </svg>
        <span class="text-2xl font-bold mr-2">{parkingSpacesDangerous}</span>
        <span class="text-sm flex items-center">
          ADR
          <svg class="w-3 h-3 ml-1 text-orange-600 dark:text-orange-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
          </svg>
        </span>
        <!-- Tooltip -->
        <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
          Hazardous materials transport parking
          <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
        </div>
      </div>
    )}
  </div>
)}
