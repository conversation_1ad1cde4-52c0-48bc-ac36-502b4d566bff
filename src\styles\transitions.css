/* Page Transitions */
.page-transition-wrapper {
  position: relative;
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.5s ease, transform 0.5s ease;
}

.page-loaded {
  opacity: 1;
  transform: translateY(0);
}

/* View Transitions Animations */
::view-transition-old(root) {
  animation: 300ms cubic-bezier(0.4, 0, 0.2, 1) both fade-out;
}

::view-transition-new(root) {
  animation: 500ms cubic-bezier(0.4, 0, 0.2, 1) 100ms both fade-in-slide-up;
}

@keyframes fade-out {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes fade-in-slide-up {
  from { 
    opacity: 0;
    transform: translateY(20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

/* Element-specific transitions */
main {
  view-transition-name: main-content;
}

header {
  view-transition-name: header;
}

footer {
  view-transition-name: footer;
}

/* Custom transitions for specific elements */
.hero-section {
  view-transition-name: hero;
}

.features-section {
  view-transition-name: features;
}

.testimonials-section {
  view-transition-name: testimonials;
}

/* Slide transitions */
.slide-left-enter {
  transform: translateX(100%);
}

.slide-left-enter-active {
  transform: translateX(0);
  transition: transform 0.5s ease-out;
}

.slide-left-exit {
  transform: translateX(0);
}

.slide-left-exit-active {
  transform: translateX(-100%);
  transition: transform 0.5s ease-in;
}

.slide-right-enter {
  transform: translateX(-100%);
}

.slide-right-enter-active {
  transform: translateX(0);
  transition: transform 0.5s ease-out;
}

.slide-right-exit {
  transform: translateX(0);
}

.slide-right-exit-active {
  transform: translateX(100%);
  transition: transform 0.5s ease-in;
} 
