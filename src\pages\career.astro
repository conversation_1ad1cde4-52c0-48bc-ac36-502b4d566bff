---
import Layout from '../layouts/Layout.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
import HeroSection from '../components/HeroSection.astro';
---

<Layout title="Careers - Sassify">
  <Header />
  <main>
    <HeroSection 
      title="Join Our Team" 
      highlightText="Team"
      description="We're looking for talented individuals to help us build the future of SaaS."
    />
    
    <section class="py-16 bg-white dark:bg-gray-800">
      <div class="container-custom">
        <div class="max-w-4xl mx-auto">
          <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">Why Work With Us?</h2>
          
          <div class="grid md:grid-cols-2 gap-8 mb-16">
            <div class="card p-6 border border-gray-200 dark:border-gray-700">
              <div class="flex items-center mb-4">
                <div class="rounded-full bg-primary-100 dark:bg-primary-900 p-3 mr-4">
                  <svg class="w-6 h-6 text-primary-600 dark:text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Flexible Work Environment</h3>
              </div>
              <p class="text-gray-600 dark:text-gray-300">
                Work from anywhere in the world. We believe in results, not hours spent at a desk.
              </p>
            </div>
            
            <div class="card p-6 border border-gray-200 dark:border-gray-700">
              <div class="flex items-center mb-4">
                <div class="rounded-full bg-primary-100 dark:bg-primary-900 p-3 mr-4">
                  <svg class="w-6 h-6 text-primary-600 dark:text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                  </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Cutting-Edge Technology</h3>
              </div>
              <p class="text-gray-600 dark:text-gray-300">
                Work with the latest technologies and tools to solve challenging problems.
              </p>
            </div>
            
            <div class="card p-6 border border-gray-200 dark:border-gray-700">
              <div class="flex items-center mb-4">
                <div class="rounded-full bg-primary-100 dark:bg-primary-900 p-3 mr-4">
                  <svg class="w-6 h-6 text-primary-600 dark:text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                  </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Diverse & Inclusive Team</h3>
              </div>
              <p class="text-gray-600 dark:text-gray-300">
                Join a global team with diverse backgrounds, perspectives, and experiences.
              </p>
            </div>
            
            <div class="card p-6 border border-gray-200 dark:border-gray-700">
              <div class="flex items-center mb-4">
                <div class="rounded-full bg-primary-100 dark:bg-primary-900 p-3 mr-4">
                  <svg class="w-6 h-6 text-primary-600 dark:text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                  </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Competitive Benefits</h3>
              </div>
              <p class="text-gray-600 dark:text-gray-300">
                Enjoy competitive salary, health benefits, stock options, and professional development opportunities.
              </p>
            </div>
          </div>
          
          <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">Open Positions</h2>
          
          <div class="space-y-6 mb-16">
            <!-- Job Listing 1 -->
            <div class="card p-6 border border-gray-200 dark:border-gray-700">
              <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2 md:mb-0">Senior Frontend Developer</h3>
                <span class="inline-block px-3 py-1 text-sm font-medium rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                  Remote
                </span>
              </div>
              <p class="text-gray-600 dark:text-gray-300 mb-4">
                We're looking for an experienced frontend developer to help us build beautiful, responsive, and accessible user interfaces for our SaaS platform.
              </p>
              <div class="flex flex-wrap gap-2 mb-6">
                <span class="inline-block px-3 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                  React
                </span>
                <span class="inline-block px-3 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                  TypeScript
                </span>
                <span class="inline-block px-3 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                  Tailwind CSS
                </span>
                <span class="inline-block px-3 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                  5+ Years Experience
                </span>
              </div>
              <a href="#" class="btn-secondary">Apply Now</a>
            </div>
            
            <!-- Job Listing 2 -->
            <div class="card p-6 border border-gray-200 dark:border-gray-700">
              <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2 md:mb-0">Backend Engineer</h3>
                <span class="inline-block px-3 py-1 text-sm font-medium rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                  Remote
                </span>
              </div>
              <p class="text-gray-600 dark:text-gray-300 mb-4">
                Join our backend team to build scalable, reliable, and secure APIs and services that power our SaaS platform.
              </p>
              <div class="flex flex-wrap gap-2 mb-6">
                <span class="inline-block px-3 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                  Node.js
                </span>
                <span class="inline-block px-3 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                  PostgreSQL
                </span>
                <span class="inline-block px-3 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                  AWS
                </span>
                <span class="inline-block px-3 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                  3+ Years Experience
                </span>
              </div>
              <a href="#" class="btn-secondary">Apply Now</a>
            </div>
            
            <!-- Job Listing 3 -->
            <div class="card p-6 border border-gray-200 dark:border-gray-700">
              <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2 md:mb-0">Product Designer</h3>
                <span class="inline-block px-3 py-1 text-sm font-medium rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                  Remote
                </span>
              </div>
              <p class="text-gray-600 dark:text-gray-300 mb-4">
                We're seeking a talented product designer to create intuitive and delightful user experiences for our customers.
              </p>
              <div class="flex flex-wrap gap-2 mb-6">
                <span class="inline-block px-3 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                  UI/UX
                </span>
                <span class="inline-block px-3 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                  Figma
                </span>
                <span class="inline-block px-3 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                  User Research
                </span>
                <span class="inline-block px-3 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                  4+ Years Experience
                </span>
              </div>
              <a href="#" class="btn-secondary">Apply Now</a>
            </div>
          </div>
          
          <div class="text-center">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Don't see a position that fits your skills?</h3>
            <p class="text-gray-600 dark:text-gray-300 mb-6">
              We're always looking for talented individuals to join our team. Send us your resume and we'll keep it on file for future opportunities.
            </p>
            <a href="/contact" class="btn-primary">Contact Us</a>
          </div>
        </div>
      </div>
    </section>
  </main>
  <Footer />
</Layout> 
