---
/**
 * Test page for cookie consent banner
 * This page helps debug cookie consent issues
 */

import Layout from '../layouts/Layout.astro';
import { getLangFromUrl, useTranslations } from '../i18n/utils';

const currentLang = getLangFromUrl(Astro.url);
const t = useTranslations(currentLang);
---

<Layout
  title="Cookie Consent Test Page"
  description="Test page for debugging cookie consent banner functionality"
  pageType="test"
>
  <main class="section">
    <div class="container-custom">
      <div class="max-w-4xl mx-auto">
        <h1 class="text-4xl font-bold text-secondary-900 dark:text-white mb-8">
          Cookie Consent Test Page
        </h1>
        
        <div class="bg-white dark:bg-secondary-800 rounded-lg shadow-lg p-6 mb-8">
          <h2 class="text-2xl font-semibold text-secondary-900 dark:text-white mb-4">
            Environment Information
          </h2>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div class="space-y-2">
              <div class="flex justify-between">
                <span class="font-medium">Production Mode:</span>
                <span class={import.meta.env.PROD ? 'text-green-600' : 'text-red-600'}>
                  {import.meta.env.PROD ? 'Yes' : 'No'}
                </span>
              </div>
              <div class="flex justify-between">
                <span class="font-medium">Cookie Consent Enabled:</span>
                <span class={import.meta.env.PUBLIC_ENABLE_COOKIE_CONSENT === 'true' ? 'text-green-600' : 'text-red-600'}>
                  {import.meta.env.PUBLIC_ENABLE_COOKIE_CONSENT}
                </span>
              </div>
              <div class="flex justify-between">
                <span class="font-medium">Dev Analytics Enabled:</span>
                <span class={import.meta.env.PUBLIC_ENABLE_DEV_ANALYTICS === 'true' ? 'text-green-600' : 'text-red-600'}>
                  {import.meta.env.PUBLIC_ENABLE_DEV_ANALYTICS}
                </span>
              </div>
              <div class="flex justify-between">
                <span class="font-medium">Analytics Enabled:</span>
                <span class={import.meta.env.PUBLIC_ENABLE_ANALYTICS === 'true' ? 'text-green-600' : 'text-red-600'}>
                  {import.meta.env.PUBLIC_ENABLE_ANALYTICS}
                </span>
              </div>
            </div>
            
            <div class="space-y-2">
              <div class="flex justify-between">
                <span class="font-medium">GA4 ID:</span>
                <span class="text-secondary-600 dark:text-secondary-300 font-mono text-xs">
                  {import.meta.env.PUBLIC_GA4_MEASUREMENT_ID || 'Not set'}
                </span>
              </div>
              <div class="flex justify-between">
                <span class="font-medium">AdSense ID:</span>
                <span class="text-secondary-600 dark:text-secondary-300 font-mono text-xs">
                  {import.meta.env.PUBLIC_ADSENSE_PUBLISHER_ID || 'Not set'}
                </span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="bg-white dark:bg-secondary-800 rounded-lg shadow-lg p-6 mb-8">
          <h2 class="text-2xl font-semibold text-secondary-900 dark:text-white mb-4">
            Cookie Consent Status
          </h2>
          
          <div id="consent-status" class="space-y-2 text-sm">
            <div>Loading consent status...</div>
          </div>
          
          <div class="mt-4 space-x-2">
            <button 
              onclick="clearConsent()" 
              class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
            >
              Clear Consent (Force Show Banner)
            </button>
            <button 
              onclick="checkBannerStatus()" 
              class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            >
              Check Banner Status
            </button>
            <button 
              onclick="debugConsent()" 
              class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
            >
              Debug Console
            </button>
          </div>
        </div>
        
        <div class="bg-white dark:bg-secondary-800 rounded-lg shadow-lg p-6 mb-8">
          <h2 class="text-2xl font-semibold text-secondary-900 dark:text-white mb-4">
            Troubleshooting Steps
          </h2>
          
          <div class="prose dark:prose-invert max-w-none">
            <ol class="space-y-2">
              <li>
                <strong>Check Environment Variables:</strong> Ensure <code>PUBLIC_ENABLE_COOKIE_CONSENT=true</code> in your .env file
              </li>
              <li>
                <strong>Development Mode:</strong> Set <code>PUBLIC_ENABLE_DEV_ANALYTICS=true</code> to show banner in development
              </li>
              <li>
                <strong>Clear Browser Storage:</strong> Click "Clear Consent" button above to reset saved preferences
              </li>
              <li>
                <strong>Check Console:</strong> Open browser developer tools and look for cookie consent debug messages
              </li>
              <li>
                <strong>Verify Alpine.js:</strong> Ensure Alpine.js is loaded and working (check console for errors)
              </li>
              <li>
                <strong>Check Z-Index:</strong> Verify banner isn't hidden behind other elements
              </li>
            </ol>
          </div>
        </div>
        
        <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-6">
          <h3 class="text-lg font-semibold text-yellow-800 dark:text-yellow-200 mb-2">
            Expected Behavior
          </h3>
          <p class="text-yellow-700 dark:text-yellow-300">
            The cookie consent banner should appear at the bottom of the page when:
          </p>
          <ul class="list-disc list-inside text-yellow-700 dark:text-yellow-300 mt-2 space-y-1">
            <li>You're in production mode, OR</li>
            <li>You're in development mode with <code>PUBLIC_ENABLE_DEV_ANALYTICS=true</code></li>
            <li>AND you haven't previously given consent (no saved preferences)</li>
            <li>AND <code>PUBLIC_ENABLE_COOKIE_CONSENT=true</code></li>
          </ul>
        </div>
      </div>
    </div>
  </main>
</Layout>

<script>
  function clearConsent() {
    localStorage.removeItem('cookie-consent');
    localStorage.removeItem('cookie-consent-date');
    console.log('🍪 Cookie consent cleared');
    updateConsentStatus();
    setTimeout(() => location.reload(), 500);
  }
  
  function checkBannerStatus() {
    const banner = document.getElementById('cookie-consent-banner');
    const debugPanel = document.getElementById('cookie-consent-debug');
    
    console.log('🍪 Banner Status Check:');
    console.log('Banner element:', banner);
    console.log('Banner visible:', banner ? (banner.offsetParent !== null) : 'Not found');
    console.log('Debug panel:', debugPanel);
    console.log('Alpine.js available:', typeof Alpine !== 'undefined');
    
    if (banner && banner._x_dataStack) {
      console.log('Alpine data:', banner._x_dataStack[0]);
    }
    
    alert(`Banner ${banner ? (banner.offsetParent !== null ? 'VISIBLE' : 'HIDDEN') : 'NOT FOUND'}`);
  }
  
  function debugConsent() {
    if (typeof window.debugCookieConsent === 'function') {
      window.debugCookieConsent();
    } else {
      console.log('🍪 Debug function not available');
    }
  }
  
  function updateConsentStatus() {
    const statusDiv = document.getElementById('consent-status');
    if (!statusDiv) return;
    
    const consent = localStorage.getItem('cookie-consent');
    const consentDate = localStorage.getItem('cookie-consent-date');
    const banner = document.getElementById('cookie-consent-banner');
    
    let html = '<div class="space-y-1">';
    
    if (consent) {
      const preferences = JSON.parse(consent);
      html += `<div><strong>Consent Status:</strong> <span class="text-green-600">Given</span></div>`;
      html += `<div><strong>Date:</strong> ${consentDate ? new Date(consentDate).toLocaleString() : 'Unknown'}</div>`;
      html += `<div><strong>Analytics:</strong> <span class="${preferences.analytics ? 'text-green-600' : 'text-red-600'}">${preferences.analytics ? 'Allowed' : 'Denied'}</span></div>`;
      html += `<div><strong>Advertising:</strong> <span class="${preferences.advertising ? 'text-green-600' : 'text-red-600'}">${preferences.advertising ? 'Allowed' : 'Denied'}</span></div>`;
    } else {
      html += `<div><strong>Consent Status:</strong> <span class="text-red-600">Not Given</span></div>`;
    }
    
    html += `<div><strong>Banner in DOM:</strong> <span class="${banner ? 'text-green-600' : 'text-red-600'}">${banner ? 'Yes' : 'No'}</span></div>`;
    
    if (banner) {
      const isVisible = banner.offsetParent !== null;
      html += `<div><strong>Banner Visible:</strong> <span class="${isVisible ? 'text-green-600' : 'text-red-600'}">${isVisible ? 'Yes' : 'No'}</span></div>`;
    }
    
    html += '</div>';
    statusDiv.innerHTML = html;
  }
  
  // Update status on page load
  document.addEventListener('DOMContentLoaded', function() {
    setTimeout(updateConsentStatus, 500);
  });
</script>
